import 'dart:math';

import 'package:confetti/confetti.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unstack/routes/animation.dart';
import 'package:unstack/routes/route.dart';

import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/views/task_details_page.dart';
import 'package:unstack/widgets/home_app_bar_button.dart';
import 'package:unstack/widgets/todo_tile.dart';
import 'package:unstack/widgets/circular_progress_3d.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  String _userName = '';
  bool isDone = false;
  late ConfettiController _confettiController;
  int _completedTasks = 0;
  bool isStreak = false;
  final List<Map<String, dynamic>> cards = [
    {'color': AppColors.backgroundSecondary, 'text': 'Card 1', 'isDone': false},
    {'color': AppColors.backgroundSecondary, 'text': 'Card 2', 'isDone': false},
    {'color': AppColors.backgroundSecondary, 'text': 'Card 3', 'isDone': false},
  ];

  @override
  void initState() {
    super.initState();
    _confettiController = ConfettiController(duration: Duration(seconds: 2));
    _loadUserName();
  }

  Future<void> _loadUserName() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final name = prefs.getString('user_name') ?? 'User';
      setState(() {
        _userName = name;
      });
    } catch (e) {
      setState(() {
        _userName = 'User';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppSpacing.lg),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: AppSpacing.xl),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      height: 50,
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSpacing.md,
                      ),
                      decoration: BoxDecoration(
                          gradient: isStreak
                              ? LinearGradient(colors: [
                                  Color(0xFFff4b1f),
                                  Color(0xFFff9068),
                                ])
                              : LinearGradient(colors: [
                                  AppColors.backgroundSecondary,
                                  AppColors.backgroundTertiary,
                                ]),
                          borderRadius:
                              BorderRadius.circular(AppBorderRadius.full),
                          border: Border.all(
                            color: isStreak
                                ? AppColors.accentOrange
                                : AppColors.textMuted,
                            width: 0.5,
                          ),
                          boxShadow: isStreak
                              ? [
                                  BoxShadow(
                                    color: AppColors.accentOrange
                                        .withValues(alpha: 0.2),
                                    blurRadius: 10,
                                    spreadRadius: 2,
                                  ),
                                ]
                              : []),
                      child: Row(
                        children: [
                          Icon(
                            CupertinoIcons.flame_fill,
                            color: isStreak
                                ? AppColors.whiteColor
                                : AppColors.textMuted,
                            size: 20,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            isStreak ? '3 streaks' : '0 streak',
                            style: TextStyle(
                              color: AppColors.whiteColor,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    )
                        .animate()
                        .slideX(
                          begin: -0.3,
                          duration: 500.ms,
                          curve: Curves.easeOut,
                        )
                        .fadeIn(delay: 200.ms),
                    Spacer(),
                    Row(
                      children: [
                        HomeAppBarButton(
                          onPressed: () {},
                          icon: CupertinoIcons.person,
                        ),
                        const SizedBox(width: AppSpacing.md),
                        HomeAppBarButton(
                          onPressed: () {
                            RouteUtils.pushNamed(
                              context,
                              RoutePaths.tasksListPage,
                            );
                          },
                          icon: CupertinoIcons.list_dash,
                        ),
                        const SizedBox(width: AppSpacing.md),
                        HomeAppBarButton(
                          onPressed: () {},
                          icon: CupertinoIcons.add,
                        ),
                      ],
                    )
                        .animate()
                        .slideX(
                          begin: 0.3,
                          duration: 500.ms,
                          curve: Curves.easeOut,
                        )
                        .fadeIn(delay: 200.ms),
                  ],
                ),

                const SizedBox(height: AppSpacing.md),
                // Welcome header
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Let's get to work,",
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    )
                        .animate()
                        .slideX(
                          begin: -0.3,
                          duration: 500.ms,
                          curve: Curves.easeOut,
                        )
                        .fadeIn(),
                    Text(
                      _userName,
                      style: AppTextStyles.h1.copyWith(
                        fontSize: 32,
                        fontWeight: FontWeight.w800,
                      ),
                    )
                        .animate()
                        .slideX(
                          begin: -0.3,
                          duration: 500.ms,
                          curve: Curves.easeOut,
                        )
                        .fadeIn(delay: 200.ms),
                  ],
                ),

                const SizedBox(height: AppSpacing.xxl),

                // 3D Circular Progress Indicator
                Center(
                  child: CircularProgressIndicator3D(
                    totalTasks: 12,
                    completedTasks: _completedTasks,
                    size: 290,
                  ),
                )
                    .animate()
                    .scale(
                      begin: const Offset(0.8, 0.8),
                      duration: 600.ms,
                      curve: Curves.easeOutBack,
                    )
                    .fadeIn(delay: 400.ms),

                Spacer(),
                Align(
                  alignment: Alignment.bottomCenter,
                  child: CardsSwiperWidget(
                    cardData: cards,
                    onCardCollectionAnimationComplete: (_) {},
                    onCardChange: (index) {
                      print('Top card index: $index');
                    },
                    cardBuilder: (context, index, visibleIndex) {
                      final card = cards[index];
                      return Hero(
                        tag: 'card_$index',
                        child: GestureDetector(
                          onTap: () {
                            // Navigator.of(context).push(
                            //   MaterialPageRoute(
                            //     builder: (context) {
                            //       return TaskDetailsPage(
                            //           heroTag: 'card_$index');
                            //     },
                            //   ),
                            // );
                            Navigator.of(context)
                                .push(HeroDialogRoute(builder: (context) {
                              return TaskDetailsPage(
                                heroTag: 'card_$index',
                              );
                            }));
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(32),
                              color: card['color'] as Color,
                              border: Border.all(
                                color: AppColors.textMuted,
                                width: 0.5,
                              ),
                            ),
                            width: 600,
                            height: 280,
                            padding: const EdgeInsets.all(AppSpacing.xl),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Transform.scale(
                                  scale: 3,
                                  child: Checkbox(
                                    value: isDone,
                                    visualDensity: VisualDensity.compact,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6.0),
                                      side: BorderSide(
                                        color: AppColors.whiteColor,
                                        width: 2,
                                      ),
                                    ),
                                    side: BorderSide(
                                      color: AppColors.textMuted,
                                      width: 2,
                                    ),
                                    activeColor: AppColors.accentGreen,
                                    onChanged: (bool? value) {
                                      setState(() {
                                        isDone = !isDone;
                                      });
                                      if (isDone) {
                                        _confettiController.play();
                                        _completedTasks++;
                                      } else {
                                        _completedTasks--;
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(height: AppSpacing.lg),
                                Text(
                                  "Build Unstack!",
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: AppTextStyles.h2.copyWith(
                                    fontFamily: 'Poppins',
                                    color: AppColors.textPrimary,
                                    fontSize: 32,
                                  ),
                                ),
                                Text(
                                  "Description of the task",
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: AppTextStyles.bodySmall.copyWith(
                                    color: AppColors.textSecondary,
                                    fontSize: 18,
                                  ),
                                ),
                                const Spacer(),
                                Row(
                                  children: [
                                    Icon(
                                      CupertinoIcons.hourglass,
                                      color: AppColors.textMuted,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      "3 Pomos",
                                      style: AppTextStyles.bodyLarge.copyWith(
                                        color: AppColors.textSecondary,
                                        fontSize: 16,
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Icon(
                                      CupertinoIcons.calendar,
                                      color: AppColors.textMuted,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      "Today",
                                      style: AppTextStyles.bodyLarge.copyWith(
                                        color: AppColors.textSecondary,
                                        fontSize: 16,
                                      ),
                                    ),
                                    const Spacer(),
                                    Icon(
                                      CupertinoIcons.flag_fill,
                                      color: AppColors.accentRed,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      "High",
                                      style: AppTextStyles.bodyLarge.copyWith(
                                        color: AppColors.accentRed,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                )
                    .animate()
                    .slideY(
                      begin: 0.3,
                      duration: 500.ms,
                      curve: Curves.easeOut,
                    )
                    .fadeIn(delay: 200.ms),
                const SizedBox(height: AppSpacing.xl),
              ],
            ),
          ),
          Align(
            alignment: Alignment.topCenter,
            child: ConfettiWidget(
              confettiController: _confettiController,
              blastDirectionality: BlastDirectionality.explosive,
              blastDirection: pi * 0.5, maxBlastForce: 10,
              numberOfParticles: 50,
              colors: const [
                AppColors.accentPurple,
                AppColors.accentBlue,
                AppColors.accentGreen,
                AppColors.accentYellow,
                AppColors.accentPink,
                AppColors.accentOrange,
              ], // manually specify the colors to be used
            ),
          ),
        ],
      ),
    );
  }
}
